# 🚀 Quick Start Guide

## ✅ What Works Right Now

### Console Application (100% Working)
The console version is fully functional and tested. It provides all the core features:

- ✅ IP geolocation lookup
- ✅ Current location detection
- ✅ Interactive console interface
- ✅ Support for IPv4 and IPv6
- ✅ Dual API support (ipinfo.io + ip-api.com fallback)
- ✅ Comprehensive location data display

## 🎯 Recommended Setup (5 minutes)

### Step 1: Build Console Version
```cmd
build-console.bat
```

### Step 2: Test the Application
```cmd
run-test.bat
```

### Step 3: Start Using It
```cmd
# Interactive mode
run-console.bat

# Quick IP lookup
run-console.bat *******

# Get help
run-console.bat --help
```

## 📱 Console Interface Features

When you run `run-console.bat`, you get:

```
============================================================
         IP GEOLOCATION LOOKUP - CONSOLE MODE
============================================================

MAIN MENU
========================================
1. Get my current location
2. Lookup specific IP address
3. Help
4. Exit

Enter your choice (1-4):
```

### Sample Output
```
==================================================
LOCATION INFORMATION
==================================================
IP Address     : *******
City           : Mountain View
Region         : California
Country        : US
Coordinates    : 37.4056,-122.0775
Organization   : AS15169 Google LLC
Postal Code    : 94043
Timezone       : America/Los_Angeles
Hostname       : dns.google

Formatted Location: Mountain View, California, US
Latitude: 37.405600
Longitude: -122.077500
View on Map: https://www.openstreetmap.org/?mlat=37.405600&mlon=-122.077500&zoom=12
==================================================
```

## 🖥️ GUI Version (Optional)

### If You Want to Try GUI:
```cmd
# Build GUI version (downloads JavaFX dependencies)
build-gui.bat

# Try to run GUI
run-gui.bat
```

**Note:** GUI may not work on all systems due to JavaFX requirements. The application will automatically fall back to console mode if GUI fails.

## 🔧 Files You Need

### Essential Files (Console Only):
- `build-console.bat` - Builds the console application
- `run-console.bat` - Runs the console application
- `run-test.bat` - Runs tests
- All Java source files in `src/`

### Additional Files (GUI):
- `build-gui.bat` - Builds GUI + console
- `run-gui.bat` - Runs GUI application
- `setup.bat` - Interactive setup

## 🎯 Command Examples

```cmd
# Build and test
build-console.bat
run-test.bat

# Basic usage
run-console.bat

# Quick lookups
run-console.bat *******
run-console.bat *******
run-console.bat **************

# Get help
run-console.bat --help
run-console.bat --version
```

## ✅ Verified Working

- ✅ Java 21 compilation
- ✅ Gson JSON parsing
- ✅ HTTP API calls to ipinfo.io
- ✅ Fallback to ip-api.com
- ✅ IPv4 and IPv6 validation
- ✅ Location data parsing
- ✅ Interactive console interface
- ✅ Command-line arguments
- ✅ Error handling
- ✅ All manual tests pass

## 🚨 If Something Doesn't Work

1. **Check Java:** `java -version` (need Java 21+)
2. **Check Compiler:** `javac -version`
3. **Clean Build:** Delete `target` and `lib` folders, then `build-console.bat`
4. **Check Internet:** The app needs internet to access APIs
5. **See Troubleshooting:** Check `TROUBLESHOOTING.md`

## 🎉 You're Ready!

The console application is production-ready and provides all the core IP geolocation functionality. The GUI is a bonus feature that may work depending on your JavaFX setup.

**Start with:** `build-console.bat` then `run-console.bat`
