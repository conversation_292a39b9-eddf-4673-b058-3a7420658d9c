@echo off
echo IP Geolocation Application - Manual Tests
echo ==========================================

if not exist "target\classes" (
    echo Error: Application not built. Please run build.bat first.
    exit /b 1
)

if not exist "lib\gson-2.10.1.jar" (
    echo Error: Dependencies not found. Please run build.bat first.
    exit /b 1
)

echo Running manual tests...
echo.

REM Run the manual test suite
java -cp "target\classes;lib\gson-2.10.1.jar" org.project.geolocation.ManualTest
