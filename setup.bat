@echo off
echo ========================================
echo IP Geolocation Application Setup
echo ========================================
echo.

REM Check Java installation
echo Checking Java installation...
java -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Java is not installed or not in PATH!
    echo Please install Java 21 or higher and try again.
    echo Download from: https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)

echo Java found:
java -version
echo.

REM Check javac (compiler)
echo Checking Java compiler...
javac -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Java compiler (javac) not found!
    echo Please install JDK (not just JRE) and try again.
    pause
    exit /b 1
)

echo Java compiler found:
javac -version
echo.

REM Ask user which version to build
echo.
echo Choose build option:
echo   1. Console only (recommended, always works)
echo   2. GUI + Console (requires JavaFX, may have issues)
echo   3. Both (build console first, then try GUI)
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo Building console version...
    call build-console.bat
) else if "%choice%"=="2" (
    echo Building GUI version...
    call build-gui.bat
) else if "%choice%"=="3" (
    echo Building console version first...
    call build-console.bat
    if %ERRORLEVEL% EQU 0 (
        echo Console build successful. Now trying GUI build...
        call build-gui.bat
    )
) else (
    echo Invalid choice. Building console version by default...
    call build-console.bat
)

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Available commands:
echo   run-console.bat    - Run in console mode
echo   run-gui.bat        - Run with GUI (if JavaFX available)
echo   run-test.bat       - Run manual tests
echo.
echo Quick start:
echo   run-console.bat    - Start the application
echo.

pause
