@echo off
echo IP Geolocation Application - GUI Mode
echo ======================================

if not exist "target\classes" (
    echo Error: Application not built. Please run build.bat first.
    exit /b 1
)

if not exist "lib\gson-2.10.1.jar" (
    echo Error: Dependencies not found. Please run build.bat first.
    exit /b 1
)

REM Check for JavaFX dependencies
if not exist "lib\javafx-controls-21.0.1.jar" (
    echo Error: JavaFX dependencies not found. Please run build.bat first.
    exit /b 1
)

echo Starting GUI application...
echo.

REM Set up JavaFX module path and classpath (try Windows-specific JARs first)
set JAVAFX_LIBS_WIN=lib\javafx-controls-21.0.1-win.jar;lib\javafx-fxml-21.0.1-win.jar;lib\javafx-base-21.0.1-win.jar;lib\javafx-graphics-21.0.1-win.jar;lib\javafx-web-21.0.1-win.jar
set JAVAFX_LIBS_GENERIC=lib\javafx-controls-21.0.1.jar;lib\javafx-fxml-21.0.1.jar;lib\javafx-base-21.0.1.jar;lib\javafx-graphics-21.0.1.jar;lib\javafx-web-21.0.1.jar

if exist "lib\javafx-controls-21.0.1-win.jar" (
    set JAVAFX_LIBS=%JAVAFX_LIBS_WIN%
    echo Using Windows-specific JavaFX libraries...
) else (
    set JAVAFX_LIBS=%JAVAFX_LIBS_GENERIC%
    echo Using generic JavaFX libraries...
)

set CLASSPATH=target\classes;lib\gson-2.10.1.jar;%JAVAFX_LIBS%

REM Try simple GUI first (no FXML dependencies)
java -cp "%CLASSPATH%" org.project.geolocation.SimpleGuiApp %*

REM If simple GUI fails, try full GUI with modules
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Simple GUI failed, trying with JavaFX modules...
    java --module-path lib --add-modules javafx.controls,javafx.fxml,javafx.web -cp "%CLASSPATH%" org.project.geolocation.GeolocationApp %*

    REM If both GUI modes fail, try with classpath only
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo JavaFX modules not available, trying with classpath...
        java -cp "%CLASSPATH%" org.project.geolocation.GeolocationApp %*

        if %ERRORLEVEL% NEQ 0 (
            echo.
            echo All GUI modes failed. Falling back to console mode...
            java -cp "target\classes;lib\gson-2.10.1.jar" org.project.geolocation.SimpleGeolocationApp %*
        )
    )
)
