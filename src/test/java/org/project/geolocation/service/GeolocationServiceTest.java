package org.project.geolocation.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.project.geolocation.model.LocationInfo;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for GeolocationService
 */
class GeolocationServiceTest {
    
    private GeolocationService geolocationService;
    
    @BeforeEach
    void setUp() {
        geolocationService = new GeolocationService();
    }
    
    @Test
    void testValidIPv4Addresses() {
        assertTrue(geolocationService.isValidIPAddress("***********"));
        assertTrue(geolocationService.isValidIPAddress("*******"));
        assertTrue(geolocationService.isValidIPAddress("***********"));
        assertTrue(geolocationService.isValidIPAddress("127.0.0.1"));
        assertTrue(geolocationService.isValidIPAddress("***************"));
        assertTrue(geolocationService.isValidIPAddress("0.0.0.0"));
    }
    
    @Test
    void testInvalidIPv4Addresses() {
        assertFalse(geolocationService.isValidIPAddress("256.1.1.1"));
        assertFalse(geolocationService.isValidIPAddress("192.168.1"));
        assertFalse(geolocationService.isValidIPAddress("***********.1"));
        assertFalse(geolocationService.isValidIPAddress("192.168.-1.1"));
        assertFalse(geolocationService.isValidIPAddress("abc.def.ghi.jkl"));
    }
    
    @Test
    void testValidIPv6Addresses() {
        assertTrue(geolocationService.isValidIPAddress("2001:db8::1"));
        assertTrue(geolocationService.isValidIPAddress("::1"));
        assertTrue(geolocationService.isValidIPAddress("fe80::1"));
        assertTrue(geolocationService.isValidIPAddress("2001:0db8:85a3:0000:0000:8a2e:0370:7334"));
    }
    
    @Test
    void testInvalidIPAddresses() {
        assertFalse(geolocationService.isValidIPAddress(null));
        assertFalse(geolocationService.isValidIPAddress(""));
        assertFalse(geolocationService.isValidIPAddress("   "));
        assertFalse(geolocationService.isValidIPAddress("not-an-ip"));
        assertFalse(geolocationService.isValidIPAddress("192.168.1"));
        assertFalse(geolocationService.isValidIPAddress("300.300.300.300"));
    }
    
    @Test
    void testGetCurrentLocationNotNull() {
        // This test requires internet connection
        // In a real environment, you might want to mock the HTTP client
        assertDoesNotThrow(() -> {
            LocationInfo location = geolocationService.getCurrentLocation();
            assertNotNull(location);
            assertNotNull(location.getIpAddress());
        });
    }
    
    @Test
    void testGetLocationByValidPublicIP() {
        // Test with Google's public DNS IP
        assertDoesNotThrow(() -> {
            LocationInfo location = geolocationService.getLocationByIP("*******");
            assertNotNull(location);
            assertEquals("*******", location.getIpAddress());
            assertNotNull(location.getCountry());
        });
    }
    
    @Test
    void testGetLocationByInvalidIP() {
        assertThrows(GeolocationException.class, () -> {
            geolocationService.getLocationByIP("999.999.999.999");
        });
    }
    
    @Test
    void testAsyncLocationLookup() {
        assertDoesNotThrow(() -> {
            geolocationService.getLocationByIPAsync("*******")
                .thenAccept(location -> {
                    assertNotNull(location);
                    assertEquals("*******", location.getIpAddress());
                })
                .join(); // Wait for completion
        });
    }
}
