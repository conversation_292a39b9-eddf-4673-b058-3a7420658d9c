package org.project.geolocation;

import org.project.geolocation.model.LocationInfo;
import org.project.geolocation.service.GeolocationException;
import org.project.geolocation.service.GeolocationService;

/**
 * Manual test class to validate the application functionality
 */
public class ManualTest {
    
    public static void main(String[] args) {
        System.out.println("=".repeat(60));
        System.out.println("IP GEOLOCATION APPLICATION - MANUAL TESTS");
        System.out.println("=".repeat(60));
        System.out.println();
        
        GeolocationService service = new GeolocationService();
        
        // Test 1: LocationInfo model
        testLocationInfoModel();
        
        // Test 2: IP validation
        testIPValidation(service);
        
        // Test 3: Google DNS lookup
        testGoogleDNSLookup(service);
        
        // Test 4: Current location (if internet available)
        testCurrentLocation(service);
        
        System.out.println("=".repeat(60));
        System.out.println("MANUAL TESTS COMPLETED");
        System.out.println("=".repeat(60));
    }
    
    private static void testLocationInfoModel() {
        System.out.println("TEST 1: LocationInfo Model");
        System.out.println("-".repeat(30));
        
        try {
            LocationInfo location = new LocationInfo("*******", "Mountain View", "CA", "US", "37.4056,-122.0775");
            
            System.out.println("✓ Constructor test passed");
            System.out.println("  IP: " + location.getIpAddress());
            System.out.println("  Location: " + location.getFormattedLocation());
            System.out.println("  Latitude: " + location.getLatitude());
            System.out.println("  Longitude: " + location.getLongitude());
            
            // Test coordinate parsing
            location.setCoordinates("40.7128,-74.0060");
            if (Math.abs(location.getLatitude() - 40.7128) < 0.001 && 
                Math.abs(location.getLongitude() - (-74.0060)) < 0.001) {
                System.out.println("✓ Coordinate parsing test passed");
            } else {
                System.out.println("✗ Coordinate parsing test failed");
            }
            
            // Test formatted location
            location.setCity("New York");
            location.setRegion("NY");
            location.setCountry("US");
            String formatted = location.getFormattedLocation();
            if ("New York, NY, US".equals(formatted)) {
                System.out.println("✓ Formatted location test passed");
            } else {
                System.out.println("✗ Formatted location test failed: " + formatted);
            }
            
        } catch (Exception e) {
            System.out.println("✗ LocationInfo model test failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testIPValidation(GeolocationService service) {
        System.out.println("TEST 2: IP Validation");
        System.out.println("-".repeat(30));
        
        String[] validIPs = {"*******", "***********", "127.0.0.1", "2001:db8::1", "::1"};
        String[] invalidIPs = {"256.1.1.1", "192.168.1", "not-an-ip", "", null};
        
        boolean allValidPassed = true;
        for (String ip : validIPs) {
            if (!service.isValidIPAddress(ip)) {
                System.out.println("✗ Valid IP failed: " + ip);
                allValidPassed = false;
            }
        }
        if (allValidPassed) {
            System.out.println("✓ All valid IP tests passed");
        }
        
        boolean allInvalidPassed = true;
        for (String ip : invalidIPs) {
            if (service.isValidIPAddress(ip)) {
                System.out.println("✗ Invalid IP passed: " + ip);
                allInvalidPassed = false;
            }
        }
        if (allInvalidPassed) {
            System.out.println("✓ All invalid IP tests passed");
        }
        
        System.out.println();
    }
    
    private static void testGoogleDNSLookup(GeolocationService service) {
        System.out.println("TEST 3: Google DNS Lookup (*******)");
        System.out.println("-".repeat(30));
        
        try {
            LocationInfo location = service.getLocationByIP("*******");
            
            if (location != null && "*******".equals(location.getIpAddress())) {
                System.out.println("✓ Google DNS lookup successful");
                System.out.println("  IP: " + location.getIpAddress());
                System.out.println("  Location: " + location.getFormattedLocation());
                System.out.println("  Organization: " + location.getOrganization());
                System.out.println("  Coordinates: " + location.getCoordinates());
            } else {
                System.out.println("✗ Google DNS lookup failed - invalid response");
            }
            
        } catch (GeolocationException e) {
            System.out.println("✗ Google DNS lookup failed: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testCurrentLocation(GeolocationService service) {
        System.out.println("TEST 4: Current Location Lookup");
        System.out.println("-".repeat(30));
        
        try {
            LocationInfo location = service.getCurrentLocation();
            
            if (location != null && location.getIpAddress() != null) {
                System.out.println("✓ Current location lookup successful");
                System.out.println("  Your IP: " + location.getIpAddress());
                System.out.println("  Your Location: " + location.getFormattedLocation());
                System.out.println("  Your ISP: " + location.getOrganization());
                System.out.println("  Your Timezone: " + location.getTimezone());
            } else {
                System.out.println("✗ Current location lookup failed - invalid response");
            }
            
        } catch (GeolocationException e) {
            System.out.println("✗ Current location lookup failed: " + e.getMessage());
            System.out.println("  This might be due to network restrictions or API limits");
        }
        
        System.out.println();
    }
}
