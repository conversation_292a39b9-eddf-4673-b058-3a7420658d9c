package org.project.geolocation;

import org.project.geolocation.console.ConsoleApp;

/**
 * Simple main class for console-only version of the IP Geolocation Application
 * This version doesn't require JavaFX and can run on any Java installation
 */
public class SimpleGeolocationApp {
    
    public static void main(String[] args) {
        System.out.println("IP Geolocation Application - Console Version");
        System.out.println("Version: 1.0.0");
        System.out.println("Java Version: " + System.getProperty("java.version"));
        System.out.println();
        
        // Parse command line arguments
        if (args.length > 0) {
            for (String arg : args) {
                switch (arg.toLowerCase()) {
                    case "--help", "-h" -> {
                        printUsage();
                        return;
                    }
                    case "--version", "-v" -> {
                        printVersion();
                        return;
                    }
                }
            }
        }
        
        // Run console application
        try {
            ConsoleApp consoleApp = new ConsoleApp();
            consoleApp.run(args);
        } catch (Exception e) {
            System.err.println("Error running application:");
            e.printStackTrace();
        }
    }
    
    private static void printUsage() {
        System.out.println("IP Geolocation Application - Console Version v1.0.0");
        System.out.println("Usage: java -jar ip-geolocation-console.jar [options] [IP_ADDRESS]");
        System.out.println();
        System.out.println("Options:");
        System.out.println("  --help, -h      Show this help message");
        System.out.println("  --version, -v   Show version information");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  java -jar ip-geolocation-console.jar");
        System.out.println("  java -jar ip-geolocation-console.jar *******");
        System.out.println();
        System.out.println("Features:");
        System.out.println("  - Lookup location by IP address");
        System.out.println("  - Get current location based on public IP");
        System.out.println("  - Support for both IPv4 and IPv6 addresses");
        System.out.println("  - Interactive console interface");
    }
    
    private static void printVersion() {
        System.out.println("IP Geolocation Application - Console Version");
        System.out.println("Version: 1.0.0");
        System.out.println("Java Version: " + System.getProperty("java.version"));
        System.out.println("Build Date: 2024");
        System.out.println();
        System.out.println("APIs Used:");
        System.out.println("  - ipinfo.io (primary)");
        System.out.println("  - ip-api.com (fallback)");
    }
}
