package org.project.geolocation;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Stage;
import org.project.geolocation.model.LocationInfo;
import org.project.geolocation.service.GeolocationException;
import org.project.geolocation.service.GeolocationService;

/**
 * Simple JavaFX GUI Application without FXML dependencies
 * This version creates the UI programmatically to avoid FXML loading issues
 */
public class SimpleGuiApp extends Application {
    
    private GeolocationService geolocationService;
    private TextField ipAddressField;
    private Button lookupButton;
    private Button currentLocationButton;
    private Label statusLabel;
    private ProgressIndicator progressIndicator;
    private TextArea resultArea;
    
    @Override
    public void start(Stage primaryStage) {
        geolocationService = new GeolocationService();
        
        // Create UI components
        createUI(primaryStage);
        
        // Show the application
        primaryStage.show();
        
        System.out.println("IP Geolocation GUI Application Started");
    }
    
    private void createUI(Stage primaryStage) {
        // Main container
        VBox root = new VBox(10);
        root.setPadding(new Insets(15));
        
        // Title
        Label titleLabel = new Label("IP Geolocation Lookup");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");
        
        // Input section
        HBox inputBox = createInputSection();
        
        // Status section
        statusLabel = new Label("Ready to lookup location...");
        statusLabel.setStyle("-fx-text-fill: green;");
        
        // Progress indicator
        progressIndicator = new ProgressIndicator();
        progressIndicator.setVisible(false);
        progressIndicator.setPrefSize(30, 30);
        
        // Results section
        Label resultsLabel = new Label("Location Information:");
        resultsLabel.setStyle("-fx-font-weight: bold;");
        
        resultArea = new TextArea();
        resultArea.setEditable(false);
        resultArea.setPrefRowCount(15);
        resultArea.setStyle("-fx-font-family: monospace;");
        resultArea.setText("No location data available. Click 'Get My Location' or enter an IP address.");
        
        // Add all components to root
        root.getChildren().addAll(
            titleLabel,
            new Separator(),
            inputBox,
            statusLabel,
            progressIndicator,
            new Separator(),
            resultsLabel,
            resultArea
        );
        
        // Create scene
        Scene scene = new Scene(new ScrollPane(root), 600, 500);
        
        // Configure stage
        primaryStage.setTitle("IP Geolocation Application");
        primaryStage.setScene(scene);
        primaryStage.setMinWidth(500);
        primaryStage.setMinHeight(400);
    }
    
    private HBox createInputSection() {
        HBox inputBox = new HBox(10);
        inputBox.setAlignment(Pos.CENTER_LEFT);
        
        Label ipLabel = new Label("IP Address:");
        
        ipAddressField = new TextField();
        ipAddressField.setPromptText("Enter IP address (optional)");
        ipAddressField.setPrefWidth(200);
        
        lookupButton = new Button("Lookup IP");
        lookupButton.setDisable(true);
        
        currentLocationButton = new Button("Get My Location");
        
        // Set up event handlers
        ipAddressField.textProperty().addListener((obs, oldText, newText) -> {
            lookupButton.setDisable(newText.trim().isEmpty());
        });
        
        lookupButton.setOnAction(e -> lookupIPAddress());
        currentLocationButton.setOnAction(e -> getCurrentLocation());
        
        inputBox.getChildren().addAll(ipLabel, ipAddressField, lookupButton, currentLocationButton);
        
        return inputBox;
    }
    
    private void lookupIPAddress() {
        String ipAddress = ipAddressField.getText().trim();
        
        if (ipAddress.isEmpty()) {
            showStatus("Please enter an IP address", true);
            return;
        }
        
        if (!geolocationService.isValidIPAddress(ipAddress)) {
            showStatus("Invalid IP address format", true);
            return;
        }
        
        performGeolocationLookup(ipAddress);
    }
    
    private void getCurrentLocation() {
        performGeolocationLookup(null);
    }
    
    private void performGeolocationLookup(String ipAddress) {
        // Show loading state
        setLoadingState(true);
        
        String lookupType = (ipAddress == null) ? "current location" : "IP: " + ipAddress;
        showStatus("Looking up " + lookupType + "...", false);
        
        // Create background task
        Task<LocationInfo> task = new Task<LocationInfo>() {
            @Override
            protected LocationInfo call() throws Exception {
                return geolocationService.getLocationByIP(ipAddress);
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    LocationInfo locationInfo = getValue();
                    displayLocationInfo(locationInfo);
                    showStatus("Location found successfully!", false);
                    setLoadingState(false);
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    String errorMessage = "Failed to get location information";
                    
                    if (exception instanceof GeolocationException) {
                        errorMessage = exception.getMessage();
                    }
                    
                    showStatus(errorMessage, true);
                    setLoadingState(false);
                    resultArea.setText("Error: " + errorMessage + "\n\nPlease check your internet connection and try again.");
                });
            }
        };
        
        // Run task in background thread
        Thread thread = new Thread(task);
        thread.setDaemon(true);
        thread.start();
    }
    
    private void displayLocationInfo(LocationInfo locationInfo) {
        if (locationInfo == null) {
            resultArea.setText("No location information available.");
            return;
        }
        
        StringBuilder result = new StringBuilder();
        result.append("=".repeat(50)).append("\n");
        result.append("LOCATION INFORMATION\n");
        result.append("=".repeat(50)).append("\n\n");
        
        appendField(result, "IP Address", locationInfo.getIpAddress());
        appendField(result, "City", locationInfo.getCity());
        appendField(result, "Region", locationInfo.getRegion());
        appendField(result, "Country", locationInfo.getCountry());
        appendField(result, "Coordinates", locationInfo.getCoordinates());
        appendField(result, "Organization", locationInfo.getOrganization());
        appendField(result, "Postal Code", locationInfo.getPostalCode());
        appendField(result, "Timezone", locationInfo.getTimezone());
        appendField(result, "Hostname", locationInfo.getHostname());
        
        result.append("\n");
        result.append("Formatted Location: ").append(locationInfo.getFormattedLocation()).append("\n");
        
        if (locationInfo.getCoordinates() != null && !locationInfo.getCoordinates().isEmpty()) {
            result.append(String.format("Latitude: %.6f%n", locationInfo.getLatitude()));
            result.append(String.format("Longitude: %.6f%n", locationInfo.getLongitude()));
            
            String mapUrl = String.format("https://www.openstreetmap.org/?mlat=%.6f&mlon=%.6f&zoom=12",
                    locationInfo.getLatitude(), locationInfo.getLongitude());
            result.append("View on Map: ").append(mapUrl).append("\n");
        }
        
        result.append("\n").append("=".repeat(50));
        
        resultArea.setText(result.toString());
    }
    
    private void appendField(StringBuilder sb, String label, String value) {
        String displayValue = (value != null && !value.isEmpty()) ? value : "N/A";
        sb.append(String.format("%-15s: %s%n", label, displayValue));
    }
    
    private void setLoadingState(boolean loading) {
        progressIndicator.setVisible(loading);
        lookupButton.setDisable(loading || ipAddressField.getText().trim().isEmpty());
        currentLocationButton.setDisable(loading);
        ipAddressField.setDisable(loading);
    }
    
    private void showStatus(String message, boolean isError) {
        statusLabel.setText(message);
        statusLabel.setStyle(isError ? "-fx-text-fill: red;" : "-fx-text-fill: green;");
    }
    
    @Override
    public void stop() throws Exception {
        super.stop();
        if (geolocationService != null) {
            geolocationService.close();
        }
        System.out.println("IP Geolocation GUI Application stopped");
    }
    
    public static void main(String[] args) {
        System.out.println("Starting IP Geolocation GUI Application...");
        System.out.println("Java Version: " + System.getProperty("java.version"));
        
        try {
            launch(args);
        } catch (Exception e) {
            System.err.println("Failed to start GUI application:");
            e.printStackTrace();
            
            // Fallback to console mode
            System.out.println("\nFalling back to console mode...");
            try {
                org.project.geolocation.console.ConsoleApp consoleApp = 
                    new org.project.geolocation.console.ConsoleApp();
                consoleApp.run(args);
            } catch (Exception consoleException) {
                System.err.println("Console mode also failed:");
                consoleException.printStackTrace();
            }
        }
    }
}
