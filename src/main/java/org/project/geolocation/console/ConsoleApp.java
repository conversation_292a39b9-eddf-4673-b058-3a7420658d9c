package org.project.geolocation.console;

import org.project.geolocation.model.LocationInfo;
import org.project.geolocation.service.GeolocationException;
import org.project.geolocation.service.GeolocationService;

import java.util.Scanner;

/**
 * Console-based version of the IP Geolocation Application
 */
public class ConsoleApp {
    
    private final GeolocationService geolocationService;
    private final Scanner scanner;
    
    public ConsoleApp() {
        this.geolocationService = new GeolocationService();
        this.scanner = new Scanner(System.in);
    }
    
    /**
     * Run the console application
     * @param args command line arguments
     */
    public void run(String[] args) {
        printWelcome();
        
        // Check if IP address was provided as argument
        if (args.length > 1 && !args[1].startsWith("-")) {
            String ipAddress = args[1];
            System.out.println("Looking up IP address: " + ipAddress);
            lookupAndDisplayLocation(ipAddress);
            return;
        }
        
        // Interactive mode
        runInteractiveMode();
    }
    
    private void printWelcome() {
        System.out.println("=".repeat(60));
        System.out.println("         IP GEOLOCATION LOOKUP - CONSOLE MODE");
        System.out.println("=".repeat(60));
        System.out.println();
    }
    
    private void runInteractiveMode() {
        boolean running = true;
        
        while (running) {
            printMenu();
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1" -> getCurrentLocation();
                case "2" -> lookupSpecificIP();
                case "3" -> printHelp();
                case "4", "q", "quit", "exit" -> {
                    running = false;
                    System.out.println("Thank you for using IP Geolocation Lookup!");
                }
                default -> System.out.println("Invalid choice. Please try again.");
            }
            
            if (running) {
                System.out.println("\nPress Enter to continue...");
                scanner.nextLine();
            }
        }
    }
    
    private void printMenu() {
        System.out.println("\n" + "=".repeat(40));
        System.out.println("MAIN MENU");
        System.out.println("=".repeat(40));
        System.out.println("1. Get my current location");
        System.out.println("2. Lookup specific IP address");
        System.out.println("3. Help");
        System.out.println("4. Exit");
        System.out.println();
        System.out.print("Enter your choice (1-4): ");
    }
    
    private void getCurrentLocation() {
        System.out.println("\nFetching your current location...");
        lookupAndDisplayLocation(null);
    }
    
    private void lookupSpecificIP() {
        System.out.print("\nEnter IP address to lookup: ");
        String ipAddress = scanner.nextLine().trim();
        
        if (ipAddress.isEmpty()) {
            System.out.println("No IP address entered.");
            return;
        }
        
        if (!geolocationService.isValidIPAddress(ipAddress)) {
            System.out.println("Invalid IP address format: " + ipAddress);
            return;
        }
        
        System.out.println("Looking up IP address: " + ipAddress);
        lookupAndDisplayLocation(ipAddress);
    }
    
    private void lookupAndDisplayLocation(String ipAddress) {
        try {
            LocationInfo locationInfo = geolocationService.getLocationByIP(ipAddress);
            displayLocationInfo(locationInfo);
        } catch (GeolocationException e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    private void displayLocationInfo(LocationInfo locationInfo) {
        if (locationInfo == null) {
            System.out.println("No location information available.");
            return;
        }
        
        System.out.println("\n" + "=".repeat(50));
        System.out.println("LOCATION INFORMATION");
        System.out.println("=".repeat(50));
        
        printField("IP Address", locationInfo.getIpAddress());
        printField("City", locationInfo.getCity());
        printField("Region", locationInfo.getRegion());
        printField("Country", locationInfo.getCountry());
        printField("Coordinates", locationInfo.getCoordinates());
        printField("Organization", locationInfo.getOrganization());
        printField("Postal Code", locationInfo.getPostalCode());
        printField("Timezone", locationInfo.getTimezone());
        printField("Hostname", locationInfo.getHostname());
        
        // Additional formatted information
        System.out.println();
        System.out.println("Formatted Location: " + locationInfo.getFormattedLocation());
        
        if (locationInfo.getCoordinates() != null && !locationInfo.getCoordinates().isEmpty()) {
            System.out.printf("Latitude: %.6f%n", locationInfo.getLatitude());
            System.out.printf("Longitude: %.6f%n", locationInfo.getLongitude());
            
            // Generate map URL
            String mapUrl = String.format("https://www.openstreetmap.org/?mlat=%.6f&mlon=%.6f&zoom=12",
                    locationInfo.getLatitude(), locationInfo.getLongitude());
            System.out.println("View on Map: " + mapUrl);
        }
        
        System.out.println("=".repeat(50));
    }
    
    private void printField(String label, String value) {
        String displayValue = (value != null && !value.isEmpty()) ? value : "N/A";
        System.out.printf("%-15s: %s%n", label, displayValue);
    }
    
    private void printHelp() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("HELP - IP GEOLOCATION LOOKUP");
        System.out.println("=".repeat(60));
        System.out.println();
        System.out.println("This application helps you find geographical location information");
        System.out.println("based on IP addresses using external geolocation services.");
        System.out.println();
        System.out.println("FEATURES:");
        System.out.println("• Get your current location based on your public IP address");
        System.out.println("• Lookup location for any valid IPv4 or IPv6 address");
        System.out.println("• Display comprehensive location details including:");
        System.out.println("  - City, Region, Country");
        System.out.println("  - GPS coordinates (latitude/longitude)");
        System.out.println("  - ISP/Organization information");
        System.out.println("  - Postal code and timezone");
        System.out.println("• Generate map links for visual location viewing");
        System.out.println();
        System.out.println("SUPPORTED IP FORMATS:");
        System.out.println("• IPv4: ***********, *******, ***********");
        System.out.println("• IPv6: 2001:db8::1, ::1, fe80::1");
        System.out.println();
        System.out.println("DATA SOURCES:");
        System.out.println("• Primary: ipinfo.io");
        System.out.println("• Fallback: ip-api.com");
        System.out.println();
        System.out.println("COMMAND LINE USAGE:");
        System.out.println("java -jar ip-geolocation-app.jar --console [IP_ADDRESS]");
        System.out.println();
        System.out.println("EXAMPLES:");
        System.out.println("java -jar ip-geolocation-app.jar --console");
        System.out.println("java -jar ip-geolocation-app.jar --console *******");
        System.out.println();
        System.out.println("NOTE: This application requires an internet connection to");
        System.out.println("access geolocation services.");
        System.out.println("=".repeat(60));
    }
    
    /**
     * Close resources
     */
    public void close() {
        if (scanner != null) {
            scanner.close();
        }
        if (geolocationService != null) {
            geolocationService.close();
        }
    }
}
