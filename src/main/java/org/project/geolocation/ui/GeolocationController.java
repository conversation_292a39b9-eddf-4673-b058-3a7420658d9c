package org.project.geolocation.ui;

import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.web.WebEngine;
import javafx.scene.web.WebView;
import org.project.geolocation.model.LocationInfo;
import org.project.geolocation.service.GeolocationException;
import org.project.geolocation.service.GeolocationService;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * JavaFX Controller for the Geolocation Application UI
 */
public class GeolocationController implements Initializable {
    
    @FXML private TextField ipAddressField;
    @FXML private Button lookupButton;
    @FXML private Button currentLocationButton;
    @FXML private Label statusLabel;
    @FXML private ProgressIndicator progressIndicator;
    
    // Location information display
    @FXML private Label ipLabel;
    @FXML private Label cityLabel;
    @FXML private Label regionLabel;
    @FXML private Label countryLabel;
    @FXML private Label coordinatesLabel;
    @FXML private Label organizationLabel;
    @FXML private Label postalCodeLabel;
    @FXML private Label timezoneLabel;
    
    // Map view
    @FXML private WebView mapWebView;
    @FXML private CheckBox showMapCheckBox;
    
    private GeolocationService geolocationService;
    private WebEngine webEngine;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        geolocationService = new GeolocationService();
        webEngine = mapWebView.getEngine();
        
        // Initialize UI state
        progressIndicator.setVisible(false);
        clearLocationDisplay();
        
        // Set up event handlers
        lookupButton.setOnAction(e -> lookupIPAddress());
        currentLocationButton.setOnAction(e -> getCurrentLocation());
        showMapCheckBox.setOnAction(e -> toggleMapView());
        
        // Enable lookup button only when IP field has text
        ipAddressField.textProperty().addListener((obs, oldText, newText) -> {
            lookupButton.setDisable(newText.trim().isEmpty());
        });
        
        // Initially disable lookup button
        lookupButton.setDisable(true);
        
        // Load initial map
        loadDefaultMap();
    }
    
    @FXML
    private void lookupIPAddress() {
        String ipAddress = ipAddressField.getText().trim();
        
        if (ipAddress.isEmpty()) {
            showStatus("Please enter an IP address", true);
            return;
        }
        
        if (!geolocationService.isValidIPAddress(ipAddress)) {
            showStatus("Invalid IP address format", true);
            return;
        }
        
        performGeolocationLookup(ipAddress);
    }
    
    @FXML
    private void getCurrentLocation() {
        performGeolocationLookup(null);
    }
    
    private void performGeolocationLookup(String ipAddress) {
        // Show loading state
        setLoadingState(true);
        clearLocationDisplay();
        
        String lookupType = (ipAddress == null) ? "current location" : "IP: " + ipAddress;
        showStatus("Looking up " + lookupType + "...", false);
        
        // Create background task
        Task<LocationInfo> task = new Task<LocationInfo>() {
            @Override
            protected LocationInfo call() throws Exception {
                return geolocationService.getLocationByIP(ipAddress);
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    LocationInfo locationInfo = getValue();
                    displayLocationInfo(locationInfo);
                    showStatus("Location found successfully!", false);
                    setLoadingState(false);
                    
                    if (showMapCheckBox.isSelected()) {
                        updateMap(locationInfo);
                    }
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    String errorMessage = "Failed to get location information";
                    
                    if (exception instanceof GeolocationException) {
                        errorMessage = exception.getMessage();
                    }
                    
                    showStatus(errorMessage, true);
                    setLoadingState(false);
                });
            }
        };
        
        // Run task in background thread
        Thread thread = new Thread(task);
        thread.setDaemon(true);
        thread.start();
    }
    
    private void displayLocationInfo(LocationInfo locationInfo) {
        if (locationInfo == null) {
            clearLocationDisplay();
            return;
        }
        
        ipLabel.setText(locationInfo.getIpAddress() != null ? locationInfo.getIpAddress() : "N/A");
        cityLabel.setText(locationInfo.getCity() != null ? locationInfo.getCity() : "N/A");
        regionLabel.setText(locationInfo.getRegion() != null ? locationInfo.getRegion() : "N/A");
        countryLabel.setText(locationInfo.getCountry() != null ? locationInfo.getCountry() : "N/A");
        coordinatesLabel.setText(locationInfo.getCoordinates() != null ? locationInfo.getCoordinates() : "N/A");
        organizationLabel.setText(locationInfo.getOrganization() != null ? locationInfo.getOrganization() : "N/A");
        postalCodeLabel.setText(locationInfo.getPostalCode() != null ? locationInfo.getPostalCode() : "N/A");
        timezoneLabel.setText(locationInfo.getTimezone() != null ? locationInfo.getTimezone() : "N/A");
    }
    
    private void clearLocationDisplay() {
        ipLabel.setText("-");
        cityLabel.setText("-");
        regionLabel.setText("-");
        countryLabel.setText("-");
        coordinatesLabel.setText("-");
        organizationLabel.setText("-");
        postalCodeLabel.setText("-");
        timezoneLabel.setText("-");
    }
    
    private void setLoadingState(boolean loading) {
        progressIndicator.setVisible(loading);
        lookupButton.setDisable(loading || ipAddressField.getText().trim().isEmpty());
        currentLocationButton.setDisable(loading);
        ipAddressField.setDisable(loading);
    }
    
    private void showStatus(String message, boolean isError) {
        statusLabel.setText(message);
        statusLabel.setStyle(isError ? "-fx-text-fill: red;" : "-fx-text-fill: green;");
    }
    
    private void toggleMapView() {
        if (showMapCheckBox.isSelected()) {
            mapWebView.setVisible(true);
            // If we have location data, update the map
            if (!coordinatesLabel.getText().equals("-") && !coordinatesLabel.getText().equals("N/A")) {
                LocationInfo currentLocation = createLocationInfoFromDisplay();
                updateMap(currentLocation);
            }
        } else {
            mapWebView.setVisible(false);
        }
    }
    
    private LocationInfo createLocationInfoFromDisplay() {
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCoordinates(coordinatesLabel.getText());
        locationInfo.setCity(cityLabel.getText());
        locationInfo.setRegion(regionLabel.getText());
        locationInfo.setCountry(countryLabel.getText());
        return locationInfo;
    }
    
    private void loadDefaultMap() {
        String defaultMapHtml = createMapHtml(0, 0, "World Map", false);
        webEngine.loadContent(defaultMapHtml);
        mapWebView.setVisible(false); // Initially hidden
    }
    
    private void updateMap(LocationInfo locationInfo) {
        if (locationInfo == null || locationInfo.getCoordinates() == null) {
            return;
        }
        
        double lat = locationInfo.getLatitude();
        double lon = locationInfo.getLongitude();
        String title = locationInfo.getFormattedLocation();
        
        String mapHtml = createMapHtml(lat, lon, title, true);
        webEngine.loadContent(mapHtml);
    }
    
    private String createMapHtml(double lat, double lon, String title, boolean showMarker) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Location Map</title>
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
                <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
                <style>
                    body { margin: 0; padding: 0; }
                    #map { height: 100vh; width: 100%%; }
                </style>
            </head>
            <body>
                <div id="map"></div>
                <script>
                    var map = L.map('map').setView([%f, %f], %d);
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© OpenStreetMap contributors'
                    }).addTo(map);
                    
                    %s
                </script>
            </body>
            </html>
            """, 
            lat, lon, showMarker ? 10 : 2,
            showMarker ? String.format("L.marker([%f, %f]).addTo(map).bindPopup('%s').openPopup();", 
                                     lat, lon, title.replace("'", "\\'")) : ""
        );
    }
}
