package org.project.geolocation.service;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import org.project.geolocation.model.LocationInfo;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;

/**
 * Service class for fetching IP geolocation data from external APIs
 */
public class GeolocationService {
    
    private static final String IPINFO_API_URL = "https://ipinfo.io/";
    private static final String IPAPI_API_URL = "http://ip-api.com/json/";
    private static final int TIMEOUT_SECONDS = 10;
    
    private final HttpClient httpClient;
    private final Gson gson;
    
    public GeolocationService() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(TIMEOUT_SECONDS))
                .build();
        this.gson = new Gson();
    }
    
    /**
     * Get location information for the current public IP address
     * @return LocationInfo object containing geolocation data
     * @throws GeolocationException if the request fails or data cannot be parsed
     */
    public LocationInfo getCurrentLocation() throws GeolocationException {
        return getLocationByIP(null); // null means current IP
    }
    
    /**
     * Get location information for a specific IP address
     * @param ipAddress the IP address to lookup (null for current IP)
     * @return LocationInfo object containing geolocation data
     * @throws GeolocationException if the request fails or data cannot be parsed
     */
    public LocationInfo getLocationByIP(String ipAddress) throws GeolocationException {
        try {
            // Try ipinfo.io first (more reliable)
            return getLocationFromIpInfo(ipAddress);
        } catch (GeolocationException e) {
            // Fallback to ip-api.com
            try {
                return getLocationFromIpApi(ipAddress);
            } catch (GeolocationException fallbackException) {
                throw new GeolocationException("Both primary and fallback geolocation services failed", e);
            }
        }
    }
    
    /**
     * Get location information asynchronously
     * @param ipAddress the IP address to lookup (null for current IP)
     * @return CompletableFuture containing LocationInfo
     */
    public CompletableFuture<LocationInfo> getLocationByIPAsync(String ipAddress) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getLocationByIP(ipAddress);
            } catch (GeolocationException e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * Fetch location data from ipinfo.io
     */
    private LocationInfo getLocationFromIpInfo(String ipAddress) throws GeolocationException {
        String url = IPINFO_API_URL;
        if (ipAddress != null && !ipAddress.trim().isEmpty()) {
            url += ipAddress.trim();
        }
        url += "?format=json";
        
        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(TIMEOUT_SECONDS))
                    .header("Accept", "application/json")
                    .header("User-Agent", "IP-Geolocation-App/1.0")
                    .GET()
                    .build();
            
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            
            if (response.statusCode() != 200) {
                throw new GeolocationException("HTTP error: " + response.statusCode() + " - " + response.body());
            }
            
            LocationInfo locationInfo = gson.fromJson(response.body(), LocationInfo.class);
            if (locationInfo == null) {
                throw new GeolocationException("Failed to parse response from ipinfo.io");
            }
            
            return locationInfo;
            
        } catch (IOException | InterruptedException e) {
            throw new GeolocationException("Network error while fetching location from ipinfo.io", e);
        } catch (JsonSyntaxException e) {
            throw new GeolocationException("Invalid JSON response from ipinfo.io", e);
        }
    }
    
    /**
     * Fetch location data from ip-api.com (fallback)
     */
    private LocationInfo getLocationFromIpApi(String ipAddress) throws GeolocationException {
        String url = IPAPI_API_URL;
        if (ipAddress != null && !ipAddress.trim().isEmpty()) {
            url += ipAddress.trim();
        }
        
        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(TIMEOUT_SECONDS))
                    .header("Accept", "application/json")
                    .header("User-Agent", "IP-Geolocation-App/1.0")
                    .GET()
                    .build();
            
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            
            if (response.statusCode() != 200) {
                throw new GeolocationException("HTTP error: " + response.statusCode() + " - " + response.body());
            }
            
            // Parse ip-api.com response and convert to our LocationInfo format
            IpApiResponse ipApiResponse = gson.fromJson(response.body(), IpApiResponse.class);
            if (ipApiResponse == null || !"success".equals(ipApiResponse.status)) {
                throw new GeolocationException("Failed to get valid response from ip-api.com");
            }
            
            return convertIpApiResponse(ipApiResponse);
            
        } catch (IOException | InterruptedException e) {
            throw new GeolocationException("Network error while fetching location from ip-api.com", e);
        } catch (JsonSyntaxException e) {
            throw new GeolocationException("Invalid JSON response from ip-api.com", e);
        }
    }
    
    /**
     * Convert ip-api.com response to our LocationInfo format
     */
    private LocationInfo convertIpApiResponse(IpApiResponse ipApiResponse) {
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setIpAddress(ipApiResponse.query);
        locationInfo.setCity(ipApiResponse.city);
        locationInfo.setRegion(ipApiResponse.regionName);
        locationInfo.setCountry(ipApiResponse.country);
        locationInfo.setPostalCode(ipApiResponse.zip);
        locationInfo.setTimezone(ipApiResponse.timezone);
        locationInfo.setOrganization(ipApiResponse.isp);
        
        // Convert lat/lon to coordinates string
        if (ipApiResponse.lat != 0.0 && ipApiResponse.lon != 0.0) {
            locationInfo.setCoordinates(ipApiResponse.lat + "," + ipApiResponse.lon);
        }
        
        return locationInfo;
    }
    
    /**
     * Validate IP address format
     * @param ipAddress IP address to validate
     * @return true if valid IPv4 or IPv6 address
     */
    public boolean isValidIPAddress(String ipAddress) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return false;
        }
        
        // Simple IPv4 validation
        String ipv4Pattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        if (ipAddress.matches(ipv4Pattern)) {
            return true;
        }
        
        // Simple IPv6 validation (basic check)
        return ipAddress.contains(":") && ipAddress.length() >= 3;
    }
    
    /**
     * Close the HTTP client resources
     */
    public void close() {
        // HttpClient doesn't need explicit closing in Java 11+
        // but this method is provided for future compatibility
    }
    
    /**
     * Inner class for ip-api.com response format
     */
    private static class IpApiResponse {
        String status;
        String country;
        String countryCode;
        String region;
        String regionName;
        String city;
        String zip;
        double lat;
        double lon;
        String timezone;
        String isp;
        String org;
        String as;
        String query;
    }
}
