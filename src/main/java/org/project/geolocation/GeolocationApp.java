package org.project.geolocation;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;

import java.io.IOException;
import java.util.Objects;

/**
 * Main JavaFX Application class for IP Geolocation Lookup
 */
public class GeolocationApp extends Application {
    
    private static final String APP_TITLE = "IP Geolocation Lookup";
    private static final String FXML_FILE = "/geolocation.fxml";
    private static final int WINDOW_WIDTH = 900;
    private static final int WINDOW_HEIGHT = 600;
    private static final int MIN_WINDOW_WIDTH = 800;
    private static final int MIN_WINDOW_HEIGHT = 500;
    
    @Override
    public void start(Stage primaryStage) {
        try {
            // Load FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource(FXML_FILE));
            Parent root = loader.load();
            
            // Create scene
            Scene scene = new Scene(root, WINDOW_WIDTH, WINDOW_HEIGHT);
            
            // Configure stage
            primaryStage.setTitle(APP_TITLE);
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(MIN_WINDOW_WIDTH);
            primaryStage.setMinHeight(MIN_WINDOW_HEIGHT);
            
            // Set application icon (if available)
            try {
                Image icon = new Image(Objects.requireNonNull(getClass().getResourceAsStream("/icon.png")));
                primaryStage.getIcons().add(icon);
            } catch (Exception e) {
                // Icon not found, continue without it
                System.out.println("Application icon not found, using default");
            }
            
            // Show the application
            primaryStage.show();
            
            // Print startup information
            System.out.println("=".repeat(50));
            System.out.println("IP Geolocation Application Started");
            System.out.println("Version: 1.0.0");
            System.out.println("Java Version: " + System.getProperty("java.version"));
            System.out.println("JavaFX Version: " + System.getProperty("javafx.version"));
            System.out.println("=".repeat(50));
            
        } catch (IOException e) {
            System.err.println("Failed to load FXML file: " + FXML_FILE);
            e.printStackTrace();
            System.exit(1);
        } catch (Exception e) {
            System.err.println("Unexpected error during application startup:");
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    @Override
    public void stop() throws Exception {
        super.stop();
        System.out.println("IP Geolocation Application stopped");
    }
    
    /**
     * Main method - entry point for the application
     * @param args command line arguments
     */
    public static void main(String[] args) {
        // Check Java version
        String javaVersion = System.getProperty("java.version");
        System.out.println("Starting IP Geolocation Application...");
        System.out.println("Java Version: " + javaVersion);
        
        // Parse command line arguments
        if (args.length > 0) {
            for (String arg : args) {
                switch (arg.toLowerCase()) {
                    case "--help", "-h" -> {
                        printUsage();
                        return;
                    }
                    case "--version", "-v" -> {
                        printVersion();
                        return;
                    }
                    case "--console", "-c" -> {
                        runConsoleMode(args);
                        return;
                    }
                }
            }
        }
        
        // Launch JavaFX application
        try {
            launch(args);
        } catch (Exception e) {
            System.err.println("Failed to start JavaFX application:");
            e.printStackTrace();
            
            // Fallback to console mode
            System.out.println("\nFalling back to console mode...");
            runConsoleMode(args);
        }
    }
    
    /**
     * Print application usage information
     */
    private static void printUsage() {
        System.out.println("IP Geolocation Application v1.0.0");
        System.out.println("Usage: java -jar ip-geolocation-app.jar [options]");
        System.out.println();
        System.out.println("Options:");
        System.out.println("  --help, -h      Show this help message");
        System.out.println("  --version, -v   Show version information");
        System.out.println("  --console, -c   Run in console mode (no GUI)");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  java -jar ip-geolocation-app.jar");
        System.out.println("  java -jar ip-geolocation-app.jar --console");
        System.out.println();
        System.out.println("Features:");
        System.out.println("  - Lookup location by IP address");
        System.out.println("  - Get current location based on public IP");
        System.out.println("  - Display location on interactive map");
        System.out.println("  - Support for both IPv4 and IPv6 addresses");
    }
    
    /**
     * Print version information
     */
    private static void printVersion() {
        System.out.println("IP Geolocation Application");
        System.out.println("Version: 1.0.0");
        System.out.println("Java Version: " + System.getProperty("java.version"));
        System.out.println("Build Date: 2024");
        System.out.println();
        System.out.println("APIs Used:");
        System.out.println("  - ipinfo.io (primary)");
        System.out.println("  - ip-api.com (fallback)");
    }
    
    /**
     * Run application in console mode (without GUI)
     */
    private static void runConsoleMode(String[] args) {
        System.out.println("Running in console mode...");
        
        try {
            org.project.geolocation.console.ConsoleApp consoleApp = 
                new org.project.geolocation.console.ConsoleApp();
            consoleApp.run(args);
        } catch (Exception e) {
            System.err.println("Console mode not available:");
            e.printStackTrace();
            System.out.println("\nPlease ensure JavaFX is properly installed for GUI mode.");
        }
    }
}
