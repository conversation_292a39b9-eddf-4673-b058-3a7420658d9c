package org.project.geolocation.model;

import com.google.gson.annotations.SerializedName;

/**
 * Data model representing location information retrieved from IP geolocation API
 */
public class LocationInfo {
    
    @SerializedName("ip")
    private String ipAddress;
    
    @SerializedName("city")
    private String city;
    
    @SerializedName("region")
    private String region;
    
    @SerializedName("country")
    private String country;
    
    @SerializedName("loc")
    private String coordinates; // Format: "latitude,longitude"
    
    @SerializedName("org")
    private String organization;
    
    @SerializedName("postal")
    private String postalCode;
    
    @SerializedName("timezone")
    private String timezone;
    
    @SerializedName("hostname")
    private String hostname;
    
    // Default constructor
    public LocationInfo() {}
    
    // Constructor with essential fields
    public LocationInfo(String ipAddress, String city, String region, String country, String coordinates) {
        this.ipAddress = ipAddress;
        this.city = city;
        this.region = region;
        this.country = country;
        this.coordinates = coordinates;
    }
    
    // Getters and Setters
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getRegion() {
        return region;
    }
    
    public void setRegion(String region) {
        this.region = region;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public String getCoordinates() {
        return coordinates;
    }
    
    public void setCoordinates(String coordinates) {
        this.coordinates = coordinates;
    }
    
    public String getOrganization() {
        return organization;
    }
    
    public void setOrganization(String organization) {
        this.organization = organization;
    }
    
    public String getPostalCode() {
        return postalCode;
    }
    
    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }
    
    public String getTimezone() {
        return timezone;
    }
    
    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }
    
    public String getHostname() {
        return hostname;
    }
    
    public void setHostname(String hostname) {
        this.hostname = hostname;
    }
    
    /**
     * Parse latitude from coordinates string
     * @return latitude as double, or 0.0 if parsing fails
     */
    public double getLatitude() {
        if (coordinates != null && coordinates.contains(",")) {
            try {
                return Double.parseDouble(coordinates.split(",")[0]);
            } catch (NumberFormatException e) {
                return 0.0;
            }
        }
        return 0.0;
    }
    
    /**
     * Parse longitude from coordinates string
     * @return longitude as double, or 0.0 if parsing fails
     */
    public double getLongitude() {
        if (coordinates != null && coordinates.contains(",")) {
            try {
                return Double.parseDouble(coordinates.split(",")[1]);
            } catch (NumberFormatException e) {
                return 0.0;
            }
        }
        return 0.0;
    }
    
    /**
     * Get formatted location string
     * @return formatted location as "City, Region, Country"
     */
    public String getFormattedLocation() {
        StringBuilder location = new StringBuilder();
        
        if (city != null && !city.isEmpty()) {
            location.append(city);
        }
        
        if (region != null && !region.isEmpty()) {
            if (location.length() > 0) location.append(", ");
            location.append(region);
        }
        
        if (country != null && !country.isEmpty()) {
            if (location.length() > 0) location.append(", ");
            location.append(country);
        }
        
        return location.toString();
    }
    
    @Override
    public String toString() {
        return "LocationInfo{" +
                "ipAddress='" + ipAddress + '\'' +
                ", city='" + city + '\'' +
                ", region='" + region + '\'' +
                ", country='" + country + '\'' +
                ", coordinates='" + coordinates + '\'' +
                ", organization='" + organization + '\'' +
                ", postalCode='" + postalCode + '\'' +
                ", timezone='" + timezone + '\'' +
                ", hostname='" + hostname + '\'' +
                '}';
    }
}
