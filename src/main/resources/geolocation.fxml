<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.web.WebView?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="org.project.geolocation.ui.GeolocationController">
   <top>
      <VBox spacing="10.0">
         <children>
            <Label style="-fx-font-size: 18px; -fx-font-weight: bold;" text="IP Geolocation Lookup" />
            <Separator />
            
            <!-- Input Section -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label text="IP Address:" />
                  <TextField fx:id="ipAddressField" prefWidth="200.0" promptText="Enter IP address (optional)" />
                  <Button fx:id="lookupButton" text="Lookup IP" />
                  <Button fx:id="currentLocationButton" text="Get My Location" />
                  <ProgressIndicator fx:id="progressIndicator" prefHeight="20.0" prefWidth="20.0" />
               </children>
            </HBox>
            
            <!-- Status Section -->
            <Label fx:id="statusLabel" text="Ready to lookup location..." />
            <Separator />
         </children>
         <padding>
            <Insets bottom="10.0" left="15.0" right="15.0" top="15.0" />
         </padding>
      </VBox>
   </top>
   
   <center>
      <HBox spacing="10.0">
         <children>
            <!-- Location Information Panel -->
            <VBox prefWidth="350.0" spacing="10.0">
               <children>
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Location Information" />
                  
                  <GridPane hgap="10.0" vgap="8.0">
                     <columnConstraints>
                        <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                        <ColumnConstraints hgrow="ALWAYS" />
                     </columnConstraints>
                     
                     <children>
                        <Label text="IP Address:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="ipLabel" text="-" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="City:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="cityLabel" text="-" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Region:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Label fx:id="regionLabel" text="-" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Country:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <Label fx:id="countryLabel" text="-" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="Coordinates:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <Label fx:id="coordinatesLabel" text="-" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        
                        <Label text="Organization:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                        <Label fx:id="organizationLabel" text="-" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                        
                        <Label text="Postal Code:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                        <Label fx:id="postalCodeLabel" text="-" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                        
                        <Label text="Timezone:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                        <Label fx:id="timezoneLabel" text="-" GridPane.columnIndex="1" GridPane.rowIndex="7" />
                     </children>
                  </GridPane>
                  
                  <Separator />
                  <CheckBox fx:id="showMapCheckBox" text="Show Location on Map" />
               </children>
               <padding>
                  <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
               </padding>
            </VBox>
            
            <!-- Map Panel -->
            <VBox HBox.hgrow="ALWAYS">
               <children>
                  <WebView fx:id="mapWebView" prefHeight="400.0" VBox.vgrow="ALWAYS" />
               </children>
               <padding>
                  <Insets bottom="15.0" left="5.0" right="15.0" top="15.0" />
               </padding>
            </VBox>
         </children>
      </HBox>
   </center>
   
   <bottom>
      <VBox>
         <children>
            <Separator />
            <Label style="-fx-font-size: 12px; -fx-text-fill: gray;" text="IP Geolocation Application v1.0 - Uses ipinfo.io and ip-api.com services">
               <padding>
                  <Insets bottom="10.0" left="15.0" right="15.0" top="10.0" />
               </padding>
            </Label>
         </children>
      </VBox>
   </bottom>
</BorderPane>
