@echo off
echo Building IP Geolocation Application (Console Only)
echo ==================================================

REM Create output directories
if not exist "lib" mkdir lib
if not exist "target" mkdir target
if not exist "target\classes" mkdir target\classes

REM Download Gson dependency if it doesn't exist
echo Checking dependencies...

if not exist "lib\gson-2.10.1.jar" (
    echo Downloading Gson library...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar' -OutFile 'lib\gson-2.10.1.jar'"
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to download Gson library!
        exit /b 1
    )
    echo Gson library downloaded successfully.
) else (
    echo Gson library already exists.
)

REM Compile Java source files (console-only classes)
echo Compiling Java sources...
javac -cp "lib\gson-2.10.1.jar" -d target\classes -sourcepath src\main\java src\main\java\org\project\geolocation\model\*.java src\main\java\org\project\geolocation\service\*.java src\main\java\org\project\geolocation\console\*.java src\main\java\org\project\geolocation\SimpleGeolocationApp.java src\main\java\org\project\geolocation\ManualTest.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    exit /b 1
)

REM Copy resources (if any)
if exist "src\main\resources" (
    echo Copying resources...
    xcopy /E /Y "src\main\resources\*" "target\classes\" >nul 2>&1
)

REM Copy dependencies to target
echo Copying dependencies...
copy "lib\gson-2.10.1.jar" "target\" >nul

echo.
echo Build completed successfully!
echo ================================
echo.
echo Available commands:
echo   run-console.bat    - Run console application
echo   run-test.bat       - Run manual tests
echo.
echo Quick start:
echo   run-console.bat
echo.
echo Examples:
echo   run-console.bat 8.8.8.8
echo   run-console.bat --help
