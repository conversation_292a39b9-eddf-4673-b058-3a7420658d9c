# IP Geolocation Application

A Java-based desktop application that fetches and displays user location information based on IP addresses using external geolocation APIs.

## Features

- 🌍 **Current Location Detection**: Automatically detect your location based on your public IP address
- 🔍 **IP Address Lookup**: Look up location information for any valid IPv4 or IPv6 address
- 🗺️ **Interactive Map**: Display location on an interactive OpenStreetMap with markers
- 📊 **Comprehensive Data**: Shows city, region, country, coordinates, ISP, postal code, and timezone
- 🖥️ **Dual Interface**: Both GUI (JavaFX) and console modes available
- 🔄 **Fallback Support**: Uses multiple APIs for reliability (ipinfo.io primary, ip-api.com fallback)
- ⚡ **Asynchronous Processing**: Non-blocking UI with progress indicators

## Screenshots

### GUI Mode
The application features a modern JavaFX interface with:
- Input field for IP address lookup
- Real-time location information display
- Interactive map integration
- Progress indicators and status updates

### Console Mode
A command-line interface for headless environments or when GUI is not available.

## Requirements

- **Java**: JDK 21 or higher (with javac compiler)
- **Internet Connection**: Required for API access and dependency download
- **Windows**: Current build scripts are for Windows (batch files)

## Installation & Build

### 1. Clone or Download the Project
```bash
# If using git
git clone <repository-url>
cd Travel_Booking

# Or download and extract the ZIP file
```

### 2. Build the Application
```bash
# Run the build script (Windows)
build.bat
```

This will:
- Download required dependencies (Gson library)
- Compile all Java source files
- Create the target directory structure
- Copy resources and dependencies

### 3. Alternative: Manual Build
```bash
# Create directories
mkdir lib target target\classes

# Download Gson dependency
# (The build script does this automatically)

# Compile manually
javac -cp "lib\gson-2.10.1.jar" -d target\classes -sourcepath src\main\java src\main\java\org\project\geolocation\**\*.java
```

## Usage

### Quick Start
```bash
# Build the application first
build.bat

# Run the application
run.bat

# Or run with specific IP address
run.bat *******
```

### Console Mode (Current Version)
```bash
# Interactive console mode
java -cp "target\classes;lib\gson-2.10.1.jar" org.project.geolocation.SimpleGeolocationApp

# Direct IP lookup
java -cp "target\classes;lib\gson-2.10.1.jar" org.project.geolocation.SimpleGeolocationApp *******

# Using the run script
run.bat --help
run.bat --version
```

### Command Line Options
```bash
# Show help
run.bat --help

# Show version
run.bat --version

# Direct IP lookup
run.bat *******
```

## API Information

### Primary API: ipinfo.io
- **URL**: https://ipinfo.io/
- **Rate Limit**: 50,000 requests/month (free tier)
- **Data Format**: JSON
- **Features**: Comprehensive location data, ISP info, timezone

### Fallback API: ip-api.com
- **URL**: http://ip-api.com/
- **Rate Limit**: 1,000 requests/minute (free tier)
- **Data Format**: JSON
- **Features**: Basic location data, ISP info

### Supported IP Formats
- **IPv4**: `***********`, `*******`, `***********`
- **IPv6**: `2001:db8::1`, `::1`, `fe80::1`

## Application Structure

```
src/
├── main/
│   ├── java/
│   │   └── org/project/geolocation/
│   │       ├── GeolocationApp.java          # Main JavaFX application
│   │       ├── console/
│   │       │   └── ConsoleApp.java          # Console mode application
│   │       ├── model/
│   │       │   └── LocationInfo.java        # Location data model
│   │       ├── service/
│   │       │   ├── GeolocationService.java  # API service layer
│   │       │   └── GeolocationException.java # Custom exception
│   │       └── ui/
│   │           └── GeolocationController.java # JavaFX controller
│   └── resources/
│       └── geolocation.fxml                 # JavaFX UI layout
└── test/
    └── java/                                # Unit tests
```

## Dependencies

- **JavaFX**: UI framework for desktop application
- **Gson**: JSON parsing and serialization
- **Java HTTP Client**: Built-in HTTP client (Java 11+)
- **JUnit**: Unit testing framework

## Development

### Running in Development
```bash
# Run with Maven
mvn javafx:run

# Run console mode
mvn exec:java -Dexec.mainClass="org.project.geolocation.GeolocationApp" -Dexec.args="--console"
```

### Testing
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=GeolocationServiceTest
```

### Building for Distribution
```bash
# Create shaded JAR with all dependencies
mvn clean package

# The executable JAR will be in target/ directory
```

## Troubleshooting

### Common Issues

1. **JavaFX Runtime Error**
   - Ensure Java 21+ is installed
   - JavaFX is included as dependency, no separate installation needed

2. **Network Connection Issues**
   - Check internet connectivity
   - Verify firewall settings allow HTTP/HTTPS requests
   - Some corporate networks may block external API access

3. **API Rate Limits**
   - Free tier limits: ipinfo.io (50k/month), ip-api.com (1k/minute)
   - Application automatically switches to fallback API if primary fails

4. **Invalid IP Address**
   - Ensure IP format is correct (IPv4: x.x.x.x, IPv6: standard format)
   - Private IP addresses may not return location data

### Performance Tips

- The application caches HTTP client instances for better performance
- Asynchronous processing prevents UI freezing during API calls
- Fallback mechanism ensures reliability

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **ipinfo.io** - Primary geolocation API service
- **ip-api.com** - Fallback geolocation API service
- **OpenStreetMap** - Map tiles for location visualization
- **Leaflet** - JavaScript library for interactive maps

## Version History

- **v1.0.0** - Initial release with GUI and console modes
  - IP geolocation lookup
  - Interactive map integration
  - Dual API support with fallback
  - Comprehensive location information display
