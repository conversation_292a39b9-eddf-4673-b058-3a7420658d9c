@echo off
echo IP Geolocation Application Runner
echo ==================================

if not exist "target\classes" (
    echo Error: Application not built. Please run build.bat first.
    exit /b 1
)

if not exist "lib\gson-2.10.1.jar" (
    echo Error: Dependencies not found. Please run build.bat first.
    exit /b 1
)

echo Starting IP Geolocation Application...
echo.

REM Run the application with all arguments passed through
java -cp "target\classes;lib\gson-2.10.1.jar" org.project.geolocation.SimpleGeolocationApp %*
