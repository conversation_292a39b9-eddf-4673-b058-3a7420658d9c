@echo off
echo Building IP Geolocation Application (GUI + Console)
echo ===================================================

REM Create output directories
if not exist "lib" mkdir lib
if not exist "target" mkdir target
if not exist "target\classes" mkdir target\classes

echo Downloading dependencies...

REM Download Gson
if not exist "lib\gson-2.10.1.jar" (
    echo Downloading Gson library...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar' -OutFile 'lib\gson-2.10.1.jar'"
)

REM Download JavaFX dependencies for Windows
if not exist "lib\javafx-controls-21.0.1-win.jar" (
    echo Downloading JavaFX Controls for Windows...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/openjfx/javafx-controls/21.0.1/javafx-controls-21.0.1-win.jar' -OutFile 'lib\javafx-controls-21.0.1-win.jar'"
)

if not exist "lib\javafx-fxml-21.0.1-win.jar" (
    echo Downloading JavaFX FXML for Windows...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/openjfx/javafx-fxml/21.0.1/javafx-fxml-21.0.1-win.jar' -OutFile 'lib\javafx-fxml-21.0.1-win.jar'"
)

if not exist "lib\javafx-base-21.0.1-win.jar" (
    echo Downloading JavaFX Base for Windows...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/openjfx/javafx-base/21.0.1/javafx-base-21.0.1-win.jar' -OutFile 'lib\javafx-base-21.0.1-win.jar'"
)

if not exist "lib\javafx-graphics-21.0.1-win.jar" (
    echo Downloading JavaFX Graphics for Windows...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/openjfx/javafx-graphics/21.0.1/javafx-graphics-21.0.1-win.jar' -OutFile 'lib\javafx-graphics-21.0.1-win.jar'"
)

if not exist "lib\javafx-web-21.0.1-win.jar" (
    echo Downloading JavaFX Web for Windows...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/openjfx/javafx-web/21.0.1/javafx-web-21.0.1-win.jar' -OutFile 'lib\javafx-web-21.0.1-win.jar'"
)

REM Set classpath for compilation
set JAVAFX_CLASSPATH=lib\javafx-controls-21.0.1-win.jar;lib\javafx-fxml-21.0.1-win.jar;lib\javafx-base-21.0.1-win.jar;lib\javafx-graphics-21.0.1-win.jar;lib\javafx-web-21.0.1-win.jar
set FULL_CLASSPATH=lib\gson-2.10.1.jar;%JAVAFX_CLASSPATH%

echo Compiling console classes first...
javac -cp "lib\gson-2.10.1.jar" -d target\classes -sourcepath src\main\java src\main\java\org\project\geolocation\model\*.java src\main\java\org\project\geolocation\service\*.java src\main\java\org\project\geolocation\console\*.java src\main\java\org\project\geolocation\SimpleGeolocationApp.java src\main\java\org\project\geolocation\ManualTest.java

if %ERRORLEVEL% NEQ 0 (
    echo Console compilation failed!
    exit /b 1
)

echo Compiling GUI classes...
javac -cp "%FULL_CLASSPATH%;target\classes" -d target\classes -sourcepath src\main\java src\main\java\org\project\geolocation\ui\*.java src\main\java\org\project\geolocation\SimpleGuiApp.java src\main\java\org\project\geolocation\GeolocationApp.java

if %ERRORLEVEL% NEQ 0 (
    echo GUI compilation failed! Console mode will still work.
    echo You can use run-console.bat for console-only functionality.
) else (
    echo GUI compilation successful!
)

REM Copy resources
if exist "src\main\resources" (
    echo Copying resources...
    xcopy /E /Y "src\main\resources\*" "target\classes\" >nul 2>&1
)

REM Copy dependencies to target
echo Copying dependencies...
copy "lib\*.jar" "target\" >nul

echo.
echo Build completed!
echo ================
echo.
echo Available commands:
echo   run-console.bat    - Run console application (always works)
echo   run-gui.bat        - Run GUI application (if JavaFX available)
echo   run-test.bat       - Run manual tests
echo.
echo Recommended: Start with run-console.bat to test functionality
