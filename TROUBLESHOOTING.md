# Troubleshooting Guide

## Common Issues and Solutions

### 1. JavaFX Issues

#### Problem: "JavaFX runtime components are missing"
**Solution:**
- The application will automatically fall back to console mode
- For GUI mode, JavaFX is downloaded automatically by `build.bat`
- If GUI still doesn't work, use `run-console.bat` instead

#### Problem: GUI window doesn't appear
**Solutions:**
1. Try running `run-gui.bat` as administrator
2. Check if JavaFX libraries were downloaded: look for `lib\javafx-*.jar` files
3. Use console mode: `run-console.bat`

### 2. Compilation Issues

#### Problem: "javac is not recognized"
**Solution:**
- Install JDK (not just JRE)
- Add Java bin directory to PATH
- Verify with: `javac -version`

#### Problem: "Cannot find symbol" errors
**Solution:**
- Run `build.bat` to download all dependencies
- Check that `lib\gson-2.10.1.jar` exists
- Clean and rebuild: delete `target` folder, then run `build.bat`

### 3. Network Issues

#### Problem: "Failed to download dependencies"
**Solutions:**
1. Check internet connection
2. Try running as administrator
3. Check firewall/antivirus settings
4. Manual download:
   - Download Gson: https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar
   - Save to `lib\gson-2.10.1.jar`

#### Problem: "Geolocation API failed"
**Solutions:**
1. Check internet connection
2. Try different IP address
3. API might be rate-limited - wait and try again
4. Corporate firewall might block external APIs

### 4. Runtime Issues

#### Problem: Application starts but shows errors
**Solutions:**
1. Run manual tests: `run-test.bat`
2. Check console output for specific error messages
3. Try with a known good IP: `run-console.bat *******`

#### Problem: "Class not found" errors
**Solution:**
- Rebuild the application: `build.bat`
- Check that `target\classes` directory exists and contains compiled classes

### 5. Windows-Specific Issues

#### Problem: "Execution policy" errors with PowerShell
**Solution:**
Run as administrator and execute:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### Problem: Batch files don't run
**Solutions:**
1. Right-click and "Run as administrator"
2. Check file associations for .bat files
3. Run from Command Prompt: `cmd /c setup.bat`

## Step-by-Step Troubleshooting

### If Nothing Works:

1. **Check Java Installation:**
   ```cmd
   java -version
   javac -version
   ```

2. **Clean Start:**
   ```cmd
   rmdir /s target
   rmdir /s lib
   setup.bat
   ```

3. **Manual Test:**
   ```cmd
   run-test.bat
   ```

4. **Try Console Mode:**
   ```cmd
   run-console.bat
   ```

5. **Check Dependencies:**
   - Verify `lib\gson-2.10.1.jar` exists (should be ~278KB)
   - Verify `target\classes` contains compiled .class files

### Getting Help

If you're still having issues:

1. Run `run-test.bat` and note any error messages
2. Check the console output for specific error details
3. Try running with a simple IP address like `*******`
4. Verify your internet connection allows access to external APIs

### Alternative: Manual Compilation

If the batch scripts don't work, try manual compilation:

```cmd
mkdir lib target target\classes

REM Download Gson manually and save to lib\gson-2.10.1.jar

javac -cp "lib\gson-2.10.1.jar" -d target\classes src\main\java\org\project\geolocation\model\*.java
javac -cp "lib\gson-2.10.1.jar;target\classes" -d target\classes src\main\java\org\project\geolocation\service\*.java
javac -cp "lib\gson-2.10.1.jar;target\classes" -d target\classes src\main\java\org\project\geolocation\console\*.java
javac -cp "lib\gson-2.10.1.jar;target\classes" -d target\classes src\main\java\org\project\geolocation\*.java

java -cp "target\classes;lib\gson-2.10.1.jar" org.project.geolocation.SimpleGeolocationApp
```
