@echo off
echo Building IP Geolocation Application...
echo =====================================

REM Create output directories
if not exist "lib" mkdir lib
if not exist "target" mkdir target
if not exist "target\classes" mkdir target\classes

REM Download dependencies if they don't exist
echo Checking dependencies...

if not exist "lib\gson-2.10.1.jar" (
    echo Downloading Gson library...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar' -OutFile 'lib\gson-2.10.1.jar'"
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to download Gson library!
        exit /b 1
    )
)

if not exist "lib\javafx-controls-21.0.1.jar" (
    echo Downloading JavaFX Controls...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/openjfx/javafx-controls/21.0.1/javafx-controls-21.0.1.jar' -OutFile 'lib\javafx-controls-21.0.1.jar'"
)

if not exist "lib\javafx-fxml-21.0.1.jar" (
    echo Downloading JavaFX FXML...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/openjfx/javafx-fxml/21.0.1/javafx-fxml-21.0.1.jar' -OutFile 'lib\javafx-fxml-21.0.1.jar'"
)

if not exist "lib\javafx-base-21.0.1.jar" (
    echo Downloading JavaFX Base...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/openjfx/javafx-base/21.0.1/javafx-base-21.0.1.jar' -OutFile 'lib\javafx-base-21.0.1.jar'"
)

if not exist "lib\javafx-graphics-21.0.1.jar" (
    echo Downloading JavaFX Graphics...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/openjfx/javafx-graphics/21.0.1/javafx-graphics-21.0.1.jar' -OutFile 'lib\javafx-graphics-21.0.1.jar'"
)

if not exist "lib\javafx-web-21.0.1.jar" (
    echo Downloading JavaFX Web...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/openjfx/javafx-web/21.0.1/javafx-web-21.0.1.jar' -OutFile 'lib\javafx-web-21.0.1.jar'"
)

REM Set classpath for compilation
set CLASSPATH=lib\gson-2.10.1.jar;lib\javafx-controls-21.0.1.jar;lib\javafx-fxml-21.0.1.jar;lib\javafx-base-21.0.1.jar;lib\javafx-graphics-21.0.1.jar;lib\javafx-web-21.0.1.jar

REM Compile Java source files
echo Compiling Java sources...
javac -cp "%CLASSPATH%" -d target\classes -sourcepath src\main\java src\main\java\org\project\geolocation\model\*.java src\main\java\org\project\geolocation\service\*.java src\main\java\org\project\geolocation\console\*.java src\main\java\org\project\geolocation\ui\*.java src\main\java\org\project\geolocation\*.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    exit /b 1
)

REM Copy resources
if exist "src\main\resources" (
    echo Copying resources...
    xcopy /E /Y "src\main\resources\*" "target\classes\"
)

REM Copy all dependencies to target
echo Copying dependencies...
copy "lib\*.jar" "target\"

echo Build completed successfully!
echo.
echo Available run modes:
echo   1. Console mode: run-console.bat
echo   2. GUI mode: run-gui.bat
echo   3. Manual test: run-test.bat
