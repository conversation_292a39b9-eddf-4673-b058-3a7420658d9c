@echo off
echo Building IP Geolocation Application...

REM Create output directories
if not exist "target" mkdir target
if not exist "target\classes" mkdir target\classes

REM Compile Java source files
echo Compiling Java sources...
javac -cp "lib\gson-2.10.1.jar" -d target\classes -sourcepath src\main\java src\main\java\org\project\geolocation\model\*.java src\main\java\org\project\geolocation\service\*.java src\main\java\org\project\geolocation\console\*.java src\main\java\org\project\geolocation\SimpleGeolocationApp.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    exit /b 1
)

REM Copy resources
if exist "src\main\resources" (
    echo Copying resources...
    xcopy /E /Y "src\main\resources\*" "target\classes\"
)

REM Create manifest file
echo Creating manifest...
echo Main-Class: org.project.geolocation.SimpleGeolocationApp > target\manifest.txt
echo Class-Path: gson-2.10.1.jar >> target\manifest.txt

REM Create JAR file
echo Creating JAR file...
cd target\classes
jar cfm ..\ip-geolocation-console.jar ..\manifest.txt org\project\geolocation\**\*.class
cd ..\..

REM Copy dependencies to target
copy "lib\gson-2.10.1.jar" "target\"

echo Build completed successfully!
echo.
echo To run the application:
echo   cd target
echo   java -jar ip-geolocation-console.jar
echo.
echo Or run directly:
echo   java -cp "target\classes;lib\gson-2.10.1.jar" org.project.geolocation.console.ConsoleApp
