@echo off
echo IP Geolocation Application - Console Mode
echo ==========================================

if not exist "target\classes" (
    echo Error: Application not built. Please run build.bat first.
    exit /b 1
)

if not exist "lib\gson-2.10.1.jar" (
    echo Error: Dependencies not found. Please run build.bat first.
    exit /b 1
)

echo Starting console application...
echo.

REM Run the console application
java -cp "target\classes;lib\gson-2.10.1.jar" org.project.geolocation.SimpleGeolocationApp %*
